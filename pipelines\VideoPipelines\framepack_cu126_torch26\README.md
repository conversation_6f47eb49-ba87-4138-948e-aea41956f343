# Hunyuan Video Generator

A Python package for generating videos from images and text prompts using the Hunyuan Video model. This implementation provides a clean, modular interface for integrating video generation capabilities into your applications.

## Features

- 🎥 Generate high-quality videos from images and text prompts
- ⚡ Optimized for both high and low VRAM environments
- 🛠️ Modular design for easy integration
- 📊 Progress tracking and previews
- 🎚️ Customizable generation parameters
- 🖥️ Command-line interface and Python API

## Prerequisites

- Python 3.8 or higher
- CUDA-compatible GPU (recommended) with at least 12GB VRAM for optimal performance
- Windows 10/11 or Linux

## Installation

1. Clone this repository:
   ```bash
   git clone https://github.com/yourusername/hunyuan-video-generator.git
   cd hunyuan-video-generator
   ```

2. Create and activate a virtual environment (recommended):
   ```bash
   python -m venv venv
   .\venv\Scripts\activate  # Windows
   # or
   source venv/bin/activate  # Linux/Mac
   ```

3. Install the required dependencies:
   ```bash
   pip install -r requirements.txt
   ```

## Usage

### Command Line Interface

```bash
python video_generator.py input.jpg "A beautiful sunset over mountains" \
    --negative_prompt "blurry, low quality, distorted" \
    --output_dir ./output \
    --seed 42 \
    --duration 4.0 \
    --steps 30 \
    --cfg 3.0
```

### Python API

```python
from video_generator import HunyuanVideoGenerator
from PIL import Image
import numpy as np

# Initialize the generator
generator = HunyuanVideoGenerator(output_dir="./output")

# Load models
generator.load_models()

# Load input image
input_image = np.array(Image.open("input.jpg").convert("RGB"))

# Generate video
def progress_callback(percentage, message, preview=None):
    print(f"\r[{percentage}%] {message}", end="", flush=True)

output_path = generator.generate_video(
    input_image=input_image,
    prompt="A beautiful sunset over mountains",
    n_prompt="blurry, low quality, distorted",
    seed=42,
    total_second_length=4.0,
    steps=30,
    progress_callback=progress_callback
)

print(f"\nVideo generated at: {output_path}")
```

## Parameters

### Required Parameters

- `input_image`: Input image as a numpy array (H, W, C)
- `prompt`: Text prompt describing the desired video

### Optional Parameters

- `n_prompt` (str, optional): Negative prompt. Default: ""
- `seed` (int, optional): Random seed. Default: 42
- `total_second_length` (float, optional): Desired video length in seconds. Default: 4.0
- `latent_window_size` (int, optional): Size of the latent window. Default: 16
- `steps` (int, optional): Number of denoising steps. Default: 50
- `cfg` (float, optional): Classifier-free guidance scale. Default: 3.0
- `gs` (float, optional): Distilled guidance scale. Default: 1.0
- `rs` (float, optional): Guidance rescale. Default: 0.0
- `gpu_memory_preservation` (float, optional): GB of GPU memory to preserve. Default: 8.0
- `use_teacache` (bool, optional): Whether to use teacher forcing cache. Default: True
- `mp4_crf` (int, optional): CRF value for MP4 encoding (lower = better quality). Default: 18
- `progress_callback` (callable, optional): Callback function for progress updates

## Examples

### Generate a 4-second video with default settings
```bash
python video_generator.py input.jpg "A beautiful sunset over mountains"
```

### Generate a longer video with higher quality
```bash
python video_generator.py input.jpg "A beautiful sunset over mountains" \
    --duration 8.0 \
    --steps 75 \
    --cfg 4.0 \
    --latent_window 24
```

### Generate a video with a negative prompt
```bash
python video_generator.py input.jpg "A futuristic city" \
    --negative_prompt "blurry, low quality, distorted, bad anatomy" \
    --seed 12345
```

## Integration with Other Applications

The `HunyuanVideoGenerator` class is designed to be easily integrated into other Python applications. You can create a custom progress UI, batch process multiple videos, or build a web interface around it.

## Troubleshooting

### Out of Memory Errors

If you encounter CUDA out of memory errors:
1. Reduce the `latent_window_size`
2. Decrease the number of `steps`
3. Increase `gpu_memory_preservation`
4. Set `use_teacache` to `False`

### Installation Issues

If you have trouble installing dependencies:
1. Make sure you have the latest version of pip:
   ```bash
   pip install --upgrade pip
   ```
2. Install PyTorch separately first:
   ```bash
   pip3 install torch torchvision torchaudio --index-url https://download.pytorch.org/whl/cu118
   ```
   (Replace `cu118` with your CUDA version or `cpu` for CPU-only)

## License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## Acknowledgments

- [Hunyuan Video](https://huggingface.co/hunyuanvideo-community/HunyuanVideo) - The base model
- [Hugging Face Diffusers](https://github.com/huggingface/diffusers) - The underlying framework
- [lllyasviel](https://github.com/lllyasviel) - For the FramePack model
