========================================
AIStudio Real-time Log: main
Started: 2025-09-03T20:40:13.215Z
File: app_main_2025-09-03_15-40-13_001.log
========================================

[2025-09-03T20:40:13.495Z] [INFO] AIStudio application started successfully
[2025-09-03T20:40:13.495Z] [INFO] [main] AIStudio application started successfully
[2025-09-03T20:40:13.520Z] [INFO] [34m[Trellis Server][39m Checking for lingering Python processes...
[2025-09-03T20:40:14.476Z] [INFO] [34m[Trellis Server][39m No Python processes found
[2025-09-03T20:40:34.428Z] [INFO] [RealtimeLogger] Started logging dependency_core to: dependency_dependency check_2025-09-03_15-40-34_001.log
[2025-09-03T20:40:34.428Z] [INFO] [dependency_core] DependencyManager: Starting dependency check for Core
[2025-09-03T20:40:36.805Z] [INFO] [dependency_core] DependencyManager: Final dependency check for Core: Python=true, Models=true, Overall=true
[2025-09-03T20:40:36.806Z] [INFO] [dependency_core] DependencyManager: Completed dependency check for Core
[2025-09-03T20:40:37.815Z] [INFO] [RealtimeLogger] Closed log stream: dependency_core
[2025-09-03T20:41:10.293Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_python installation_2025-09-03_15-41-10_001.log
[2025-09-03T20:41:10.293Z] [INFO] [dependency_video_generation] DependencyManager: Starting python installation for Video Generation
[2025-09-03T20:41:10.294Z] [INFO] [dependency_video_generation] DependencyManager: Installing dependencies for Video Generation (python:all)
[2025-09-03T20:41:10.294Z] [INFO] [dependency_video_generation] DependencyManager: Component type: string, Component value: 'python'
[2025-09-03T20:41:10.295Z] [INFO] [dependency_video_generation] DependencyManager: Name type: string, Name value: 'all'
[2025-09-03T20:41:10.295Z] [INFO] [dependency_video_generation] DependencyManager: About to check routing for Video Generation with component python
[2025-09-03T20:41:10.297Z] [INFO] [RealtimeLogger] Started logging dependency_video_generation to: dependency_bundled installation_2025-09-03_15-41-10_001.log
[2025-09-03T20:41:10.298Z] [INFO] [dependency_video_generation] DependencyManager: Starting bundled installation for Video Generation
[2025-09-03T20:41:10.299Z] [INFO] [dependency_video_generation] DependencyManager: Starting portable installation of Video Generation module
[2025-09-03T20:41:10.300Z] [INFO] [dependency_video_generation] DependencyManager: Source: N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip
[2025-09-03T20:41:10.300Z] [INFO] [dependency_video_generation] DependencyManager: Extraction Target: N:\AIStudio\pipelines\VideoPipelines
[2025-09-03T20:41:10.301Z] [INFO] [dependency_video_generation] DependencyManager: Checking for existing installation...
[2025-09-03T20:41:10.302Z] [INFO] [dependency_video_generation] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-03T20:41:11.080Z] [INFO] [dependency_video_generation] DependencyManager: PowerShell cleanup successful
[2025-09-03T20:41:11.081Z] [INFO] [dependency_video_generation] DependencyManager: Cleanup completed successfully
[2025-09-03T20:41:11.083Z] [INFO] [dependency_video_generation] DependencyManager: Parent directory prepared
[2025-09-03T20:41:11.083Z] [INFO] [dependency_video_generation] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip
[2025-09-03T20:41:11.084Z] [INFO] [dependency_video_generation] DependencyManager: Using PowerShell-based extraction (same as 3D modules)
[2025-09-03T20:41:11.084Z] [INFO] [dependency_video_generation] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip to N:\AIStudio\pipelines\VideoPipelines
[2025-09-03T20:41:11.085Z] [INFO] [dependency_video_generation] DependencyManager: Using PowerShell-based extraction (same as 3D modules)
[2025-09-03T21:03:02.805Z] [INFO] [dependency_video_generation] DependencyManager: Extracted contents: 2 files found
[2025-09-03T21:03:02.806Z] [INFO] [dependency_video_generation] DependencyManager: Extraction completed successfully
[2025-09-03T21:03:02.807Z] [INFO] [dependency_video_generation] DependencyManager: Running post-installation finalization...
[2025-09-03T21:03:02.807Z] [INFO] [dependency_video_generation] DependencyManager: Starting finalization of Video Generation module
[2025-09-03T21:03:02.808Z] [INFO] [dependency_video_generation] DependencyManager: Running post-extraction setup...
[2025-09-03T21:03:03.822Z] [INFO] [dependency_video_generation] DependencyManager: Finalization completed successfully
[2025-09-03T21:03:03.823Z] [INFO] [dependency_video_generation] DependencyManager: Video Generation module installation completed successfully
[2025-09-03T21:03:03.824Z] [INFO] [dependency_video_generation] DependencyManager: Verifying installation...
[2025-09-03T21:03:03.931Z] [INFO] [dependency_video_generation] DependencyManager: Checking directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-03T21:03:03.992Z] [INFO] [dependency_video_generation] DependencyManager: Found directory: framepack_cu126_torch26
[2025-09-03T21:03:03.992Z] [INFO] [dependency_video_generation] DependencyManager: Checking directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system
[2025-09-03T21:03:04.052Z] [INFO] [dependency_video_generation] DependencyManager: Found directory: framepack_cu126_torch26\system
[2025-09-03T21:03:04.053Z] [INFO] [dependency_video_generation] DependencyManager: Checking file: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\run.bat
[2025-09-03T21:03:04.114Z] [INFO] [dependency_video_generation] DependencyManager: Found file: framepack_cu126_torch26\run.bat
[2025-09-03T21:03:04.115Z] [INFO] [dependency_video_generation] DependencyManager: Checking file: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\update.bat
[2025-09-03T21:03:04.176Z] [INFO] [dependency_video_generation] DependencyManager: Found file: framepack_cu126_torch26\update.bat
[2025-09-03T21:03:04.176Z] [INFO] [dependency_video_generation] DependencyManager: Checking file: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\environment.bat
[2025-09-03T21:03:04.237Z] [INFO] [dependency_video_generation] DependencyManager: Found file: framepack_cu126_torch26\environment.bat
[2025-09-03T21:03:04.238Z] [INFO] [dependency_video_generation] DependencyManager: Installation verification successful
[2025-09-03T21:03:04.249Z] [INFO] [dependency_video_generation] DependencyManager: Video Generation module installation completed successfully
[2025-09-03T21:03:04.250Z] [INFO] [dependency_video_generation] DependencyManager: Completed bundled installation for Video Generation
[2025-09-03T21:03:05.293Z] [INFO] [RealtimeLogger] Closed log stream: dependency_video_generation
