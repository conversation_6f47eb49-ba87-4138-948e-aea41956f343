========================================
AIStudio Real-time Log: dependency_video_generation
Started: 2025-09-03T21:59:48.633Z
File: dependency_bundled installation_2025-09-03_16-59-48_001.log
========================================

[2025-09-03T21:59:48.633Z] [INFO] DependencyManager: Starting bundled installation for Video Generation
[2025-09-03T21:59:48.634Z] [INFO] DependencyManager: Starting portable installation of Video Generation module
[2025-09-03T21:59:48.635Z] [INFO] DependencyManager: Source: N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip
[2025-09-03T21:59:48.635Z] [INFO] DependencyManager: Extraction Target: N:\AIStudio\pipelines\VideoPipelines
[2025-09-03T21:59:48.636Z] [INFO] DependencyManager: Checking for existing installation...
[2025-09-03T21:59:48.637Z] [INFO] DependencyManager: Removing existing directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-03T22:01:49.875Z] [INFO] DependencyManager: PowerShell cleanup successful
[2025-09-03T22:01:49.877Z] [INFO] DependencyManager: Cleanup completed successfully
[2025-09-03T22:01:49.878Z] [INFO] DependencyManager: Parent directory prepared
[2025-09-03T22:01:49.879Z] [INFO] DependencyManager: Verifying source zip: N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip
[2025-09-03T22:01:49.879Z] [INFO] DependencyManager: Using PowerShell-based extraction (same as 3D modules)
[2025-09-03T22:01:49.880Z] [INFO] DependencyManager: Starting extraction of N:\AIStudio\src\module_source\VideoGen\framepack_cu126_torch26.zip to N:\AIStudio\pipelines\VideoPipelines
[2025-09-03T22:01:49.880Z] [INFO] DependencyManager: Using PowerShell-based extraction (same as 3D modules)
[2025-09-03T22:21:48.282Z] [INFO] DependencyManager: Extracted contents: 2 files found
[2025-09-03T22:21:48.283Z] [INFO] DependencyManager: Extraction completed successfully
[2025-09-03T22:21:48.284Z] [INFO] DependencyManager: Running post-installation finalization...
[2025-09-03T22:21:48.284Z] [INFO] DependencyManager: Starting finalization of Video Generation module
[2025-09-03T22:21:48.285Z] [INFO] DependencyManager: Running post-extraction setup...
[2025-09-03T22:21:49.287Z] [INFO] DependencyManager: Finalization completed successfully
[2025-09-03T22:21:49.288Z] [INFO] DependencyManager: Video Generation module installation completed successfully
[2025-09-03T22:21:49.288Z] [INFO] DependencyManager: Verifying installation...
[2025-09-03T22:21:49.396Z] [INFO] DependencyManager: Checking directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26
[2025-09-03T22:21:49.458Z] [INFO] DependencyManager: Found directory: framepack_cu126_torch26
[2025-09-03T22:21:49.459Z] [INFO] DependencyManager: Checking directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\venv
[2025-09-03T22:21:49.518Z] [INFO] DependencyManager: Found directory: framepack_cu126_torch26\venv
[2025-09-03T22:21:49.519Z] [INFO] DependencyManager: Checking directory: N:\AIStudio\pipelines\VideoPipelines\framepack_cu126_torch26\system
[2025-09-03T22:21:49.581Z] [INFO] DependencyManager: Found directory: framepack_cu126_torch26\system
[2025-09-03T22:21:49.582Z] [INFO] DependencyManager: Installation verification successful
[2025-09-03T22:21:49.592Z] [INFO] DependencyManager: Video Generation module installation completed successfully
[2025-09-03T22:21:49.593Z] [INFO] DependencyManager: Completed bundled installation for Video Generation

========================================
Log ended: 2025-09-03T22:21:50.607Z
========================================
