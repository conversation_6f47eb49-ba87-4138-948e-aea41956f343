import sys
import os
from pathlib import Path

# Add the current directory and webui directory to Python path
current_dir = Path(__file__).parent
sys.path.append(str(current_dir))
sys.path.append(str(current_dir / "webui"))

try:
    from video_generator import HunyuanVideoGenerator
    from PIL import Image
    import numpy as np
    import torch
except ImportError as e:
    print(f"Import error: {e}")
    print("Make sure all required packages are installed.")
    sys.exit(1)

def main():
    # Set up paths
    base_dir = Path(__file__).parent
    test_image_path = base_dir / "test_image" / "Create an anime imag.png"
    output_dir = base_dir / "test_output"
    models_dir = base_dir / "models"

    # Check if test image exists
    if not test_image_path.exists():
        print(f"Error: Test image not found at {test_image_path}")
        print("Please ensure the test image exists before running the script.")
        return

    # Create necessary directories if they don't exist
    output_dir.mkdir(parents=True, exist_ok=True)
    models_dir.mkdir(parents=True, exist_ok=True)

    # Check CUDA availability
    cuda_available = torch.cuda.is_available()
    device = "cuda" if cuda_available else "cpu"
    print(f"CUDA available: {cuda_available}")
    print(f"Using device: {device}")

    if cuda_available:
        print(f"CUDA device: {torch.cuda.get_device_name()}")
        print(f"CUDA memory: {torch.cuda.get_device_properties(0).total_memory / 1024**3:.1f} GB")

    print("Initializing Hunyuan Video Generator...")
    try:
        # Initialize with detected device
        generator = HunyuanVideoGenerator(
            output_dir=str(output_dir),
            models_dir=str(models_dir),
            device=device,
            high_vram=False  # Use conservative memory settings
        )
        
        print("Loading models...")
        generator.load_models()
        
        # Load test image
        print(f"Loading test image: {test_image_path}")
        input_image = np.array(Image.open(test_image_path).convert("RGB"))
        
        # Simple progress callback
        def progress_callback(percentage, message, preview=None):
            print(f"\r[{percentage}%] {message}", end="")
            if percentage == 100:
                print()  # New line at 100%
        
        # Test with minimal settings for quick verification
        print("\nStarting video generation (this may take a while)...")
        output_path = generator.generate_video(
            input_image=input_image,
            prompt="Anime character in a magical forest, beautiful lighting, detailed background",
            n_prompt="blurry, low quality, distorted, bad anatomy, extra limbs",
            seed=42,
            total_second_length=2.0,  # Very short for testing
            latent_window_size=8,     # Reduced for testing
            steps=1,                 # Minimal steps for testing
            cfg=3.0,
            gs=1.0,
            rs=0.0,
            gpu_memory_preservation=3.0,
            use_teacache=True,
            mp4_crf=23,
            progress_callback=progress_callback
        )
        
        print(f"\nTest completed successfully!")
        print(f"Output video saved to: {output_path}")
        
    except Exception as e:
        print(f"\nError during test: {str(e)}")
        import traceback
        traceback.print_exc()
    finally:
        # Clean up
        if 'generator' in locals():
            generator.unload_models()
        print("Test completed.")

if __name__ == "__main__":
    main()
